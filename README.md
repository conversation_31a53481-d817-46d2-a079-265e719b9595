# User-DF 用户数据处理系统

User-DF 是一个高性能的用户数据处理系统，专为大规模用户数据的存储、处理和向量化而设计。系统采用微服务架构，支持20亿用户规模的数据处理，具备高并发、高可用和高性能的特点。

## 🏗️ 系统架构

```
User-DF/
├── services/                    # 微服务模块
│   ├── orc_mongodb_service/     # ORC文件处理和MongoDB存储服务
│   └── user_vector_service/     # 用户向量化处理服务
├── shared/                      # 共享模块
│   ├── core/                    # 核心功能（配置、日志、异常）
│   ├── database/                # 数据库连接模块
│   ├── models/                  # 数据模型
│   ├── queue/                   # 队列管理
│   └── utils/                   # 工具函数
├── configs/                     # 配置文件
├── models/                      # 预训练模型
├── logs/                        # 日志文件
└── tools/                       # 工具脚本
```

## 🚀 核心特性

- **大规模处理**: 支持20亿用户数据的高效处理
- **微服务架构**: 模块化设计，易于扩展和维护
- **批处理优化**: 智能批处理机制，提升处理效率
- **向量化支持**: 集成Milvus向量数据库，支持高维向量存储和检索
- **高可用性**: 完善的错误处理和恢复机制
- **监控完善**: 全面的日志记录和性能监控

## 🛠️ 环境要求

- Python 3.8+
- MongoDB 4.4+
- Milvus 2.5+
- Redis 6.0+ (可选)

## 📦 安装

```bash
# 克隆项目
git clone https://github.com/user-df/User-DF.git
cd User-DF

# 安装依赖
pip install -e .

# 或使用requirements.txt
pip install -r requirements.txt
```

## 🚀 快速开始

### 1. 配置环境

```bash
# 编辑配置文件，设置数据库连接信息
vim configs/orc_mongodb_service/development.yaml
vim configs/user_vector_service/development.yaml
```

### 2. 启动服务

```bash
# 启动ORC MongoDB服务
python3 services/orc_mongodb_service/start_services.py

# 启动用户向量服务
python3 services/user_vector_service/start_services.py
```

### 3. 检查服务状态

```bash
# 检查ORC MongoDB服务状态
python3 services/orc_mongodb_service/start_services.py --status

# 检查用户向量服务状态
python3 services/user_vector_service/start_services.py --status
```

## 📊 服务详情

### ORC MongoDB 服务
处理ORC文件数据并存储到MongoDB，包含两个微服务：
- **ORC处理微服务** (端口 8001): 处理ORC文件读取和数据解析
- **MongoDB写入微服务** (端口 8002): 负责用户数据的MongoDB存储

详细说明请参考: [ORC MongoDB服务文档](services/orc_mongodb_service/README.md)

### 用户向量服务
处理用户向量化计算和存储，包含三个微服务：
- **MongoDB读取微服务** (端口 8003): 从MongoDB读取用户数据
- **向量计算微服务** (端口 8004): 执行PCA降维和向量计算
- **向量存储微服务** (端口 8005): 将用户向量存储到Milvus

详细说明请参考: [用户向量服务文档](services/user_vector_service/README.md)

## 🔧 配置管理

系统使用YAML配置文件进行管理，每个服务都有独立的配置目录：

```
configs/
├── orc_mongodb_service/
│   ├── development.yaml          # 开发环境主配置
│   ├── production.yaml           # 生产环境主配置
│   ├── orc_processor_service/    # ORC处理服务配置
│   └── mongodb_writer_service/   # MongoDB写入服务配置
└── user_vector_service/
    ├── development.yaml          # 开发环境主配置
    ├── production.yaml           # 生产环境主配置
    ├── mongodb_reader_service/   # MongoDB读取服务配置
    ├── vector_processor_service/ # 向量计算服务配置
    └── vector_writer_service/    # 向量存储服务配置
```

## 📈 性能特性

- **批处理优化**: 支持1000用户/批次的高效处理
- **内存管理**: 智能的内存使用和垃圾回收
- **并发处理**: 多进程和异步处理支持
- **数据压缩**: 优化的数据存储格式
- **缓存机制**: 智能缓存提升查询性能

## 🔍 监控和日志

- **结构化日志**: 统一的日志格式和级别管理
- **性能监控**: 实时的性能指标收集
- **健康检查**: 自动的服务健康状态检测
- **错误追踪**: 完善的错误记录和追踪机制

## 🛠️ 开发指南

### 本地开发

```bash
# 安装开发依赖
pip install -e .[dev]

# 运行测试
pytest tests/

# 代码格式化
black .
isort .
```

### 调试模式

```bash
# 启动调试模式
python3 services/orc_mongodb_service/start_services.py --config configs/orc_mongodb_service/development.yaml

# 查看详细日志
tail -f logs/*.log
```

## 🚨 故障排除

常见问题的解决方案请参考：
- [故障排除指南](docs/TROUBLESHOOTING.md)
- [配置说明](docs/CONFIGURATION.md)
- [API文档](docs/API.md)

## 📞 支持

如有问题或建议，请：
- 查看项目文档
- 提交GitHub Issue
- 联系开发团队

## 📄 许可证

本项目采用MIT许可证，详情请参见[LICENSE](LICENSE)文件。

---

**User-DF Team** - 专注于大规模用户数据处理解决方案