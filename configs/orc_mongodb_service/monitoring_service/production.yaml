# 监控服务 - 开发环境配置
# 版本: 1.0.0

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "1.0.0"
  environment: "production"

# ==================== 服务配置 ====================
service:
  name: "monitoring_service"

# ==================== 被监控服务配置 ====================
services:
  orc_processor:
    name: "ORC处理服务"
    url: "http://localhost:42001"
    health_endpoint: "/health"
    stats_endpoint: "/stats"
    
  mongodb_writer:
    name: "MongoDB写入服务"
    url: "http://localhost:42002"
    health_endpoint: "/health"
    stats_endpoint: "/stats"

# ==================== Redis配置 ====================
redis:
  host: "localhost"
  port: 11005
  db: 0
  password: ""
  
  # 队列配置
  queue_name: "mongodb_write_queue"

# ==================== 监控配置 ====================
monitoring:
  # 检查间隔（秒）
  check_interval: 5
  # 请求超时（秒）
  request_timeout: 5
  # 显示刷新频率（每秒）
  refresh_rate: 1

# ==================== 告警配置 ====================
alerts:
  # 是否启用告警
  enabled: true
  
  # 服务不健康告警
  service_unhealthy:
    enabled: true
    threshold: 3  # 连续失败次数
  
  # 队列积压告警
  queue_backlog:
    enabled: true
    threshold: 1000  # 队列长度阈值
  
  # 处理延迟告警
  processing_delay:
    enabled: true
    threshold: 300  # 延迟秒数阈值

# ==================== 通知配置 ====================
notifications:
  # 邮件通知
  email:
    enabled: false
    smtp_server: "smtp.example.com"
    smtp_port: 587
    username: ""
    password: ""
    from_email: "<EMAIL>"
    to_emails: ["<EMAIL>"]
  
  # 钉钉通知
  dingtalk:
    enabled: false
    webhook_url: ""
    secret: ""

# ==================== 数据存储配置 ====================
storage:
  # 是否保存监控数据
  enabled: true
  # 数据保存路径
  data_path: "logs/monitoring_service/monitoring_data.json"
  # 数据保留天数
  retention_days: 7
  # 保存间隔（秒）
  save_interval: 60

# ==================== 日志配置 ====================
logging:
  level: INFO
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file_enabled: true
  file_path: logs/orc_mongodb_service/monitoring_service/monitoring_service.log
  file_max_size: "100MB"
  file_backup_count: 5
  console_enabled: true
  console_colored: false
  structured: false

# ==================== 显示配置 ====================
display:
  # 控制台显示模式
  mode: "rich"  # rich, simple, json
  # 是否显示详细信息
  show_details: true
  # 是否显示历史趋势
  show_trends: false
  # 表格最大行数
  max_table_rows: 20
