# ORC MongoDB 服务

ORC MongoDB 服务是 User-DF 系统的核心数据处理服务，负责从 ORC 文件中读取用户数据并存储到 MongoDB 数据库中。该服务采用微服务架构，支持大规模数据处理和高并发访问。

## 🏗️ 服务架构

该服务包含两个微服务：

### ORC 处理微服务 (端口 8001)

- **功能**: 读取和解析 ORC 文件
- **特性**: 
  - 支持分区数据处理 (prov_id/statis_ymd)
  - 批量数据读取 (1000 用户/批次)
  - 内存优化的数据处理
  - 支持多种 ORC 文件格式

### MongoDB 写入微服务 (端口 8002)

- **功能**: 将处理后的数据写入 MongoDB
- **特性**:
  - 批量写入优化
  - 数据去重和验证
  - 支持 20 亿用户规模
  - 自动索引管理

## 🚀 快速开始

### 启动服务

```bash
# 启动所有微服务
python3 services/orc_mongodb_service/start_services.py

# 使用指定配置启动
python3 services/orc_mongodb_service/start_services.py --config configs/orc_mongodb_service/production.yaml

# 检查服务状态
python3 services/orc_mongodb_service/start_services.py --status

# 停止所有服务
python3 services/orc_mongodb_service/start_services.py --stop
```

### 服务管理

```bash
# 查看服务进程
ps aux | grep orc_mongodb_service

# 监控服务状态
python3 -m services.orc_mongodb_service.monitoring_service.monitor

# 查看实时日志
tail -f logs/orc_mongodb_service_*.log
```

## ⚙️ 配置说明

### 主要配置文件

- `configs/orc_mongodb_service/development.yaml` - 开发环境配置
- `configs/orc_mongodb_service/production.yaml` - 生产环境配置

### 核心配置项

```yaml
# ORC 文件处理配置
orc_processing:
  batch_size: 1000                    # 批处理大小
  chunk_size: 1000                    # 分块大小
  file_pattern: "*.orc*"              # 文件匹配模式
  base_path: "orc_data"               # 数据基础路径

# MongoDB 配置
mongodb:
  connection_string: "mongodb://localhost:27017"
  database: "nrdc"
  collection: "user_pid_records_optimized"
  batch_size: 1000                    # 批量写入大小
  
# 性能优化配置
performance:
  batch_delay: 0.1                    # 批次间延迟
  file_delay_multiplier: 0.001        # 文件处理延迟倍数
  max_memory_usage: "8GB"             # 最大内存使用
```

## 📊 数据处理流程

1. **文件扫描**: 扫描指定目录下的 ORC 文件
2. **分批读取**: 按批次读取用户数据 (1000 用户/批次)
3. **数据处理**: 
   - PID 去重和验证
   - 数据格式转换
   - 时间戳处理
4. **Milvus 查询**: 验证 PID 在向量数据库中的存在性
5. **MongoDB 存储**: 批量写入处理后的数据

## 🗄️ 数据模型

### MongoDB 文档结构

```json
{
  "_id": 123456789,                    # 用户ID (INT32)
  "uid": 123456789,                    # 用户ID
  "pid_groups": [                      # PID分组数组
    {
      "timestamp_days": 19723,         # 时间戳(天数)
      "pids": [1001, 1002, 1003]       # PID列表
    }
  ],
  "pid_count": 300,                    # PID总数
  "created_days": 19723,               # 创建时间(天数)
  "updated_days": 19723,               # 更新时间(天数)
  "vector_status": {                   # 向量状态
    "is_stored": false,                # 是否已存储向量
    "stored_at_days": null             # 存储时间(天数)
  },
  "prov_id": 100                       # 省份ID
}
```

## 📈 性能特性

- **高吞吐量**: 支持每秒处理数千用户数据
- **内存优化**: 分块处理避免内存溢出
- **批量操作**: 批量读取和写入提升性能
- **并发处理**: 多微服务并行处理
- **错误恢复**: 自动重试和错误处理机制

## 🔍 监控和日志

### 日志文件

- `logs/orc_mongodb_service_YYYYMMDD_HHMMSS.log` - 主服务日志
- 每个微服务独立运行，可单独查看日志

### 监控指标

- 处理速度 (用户/秒)
- 内存使用情况
- MongoDB 连接状态
- 错误率和重试次数
- 文件处理进度

## 🛠️ 开发和调试

### 本地开发

```bash
# 安装开发依赖
pip install -e .[dev]

# 运行单元测试
pytest tests/services/orc_mongodb_service/

# 代码格式化
black services/orc_mongodb_service/
isort services/orc_mongodb_service/
```

### 调试模式

```bash
# 查看详细日志
tail -f logs/orc_mongodb_service_*.log

# 验证配置文件
python3 services/orc_mongodb_service/start_services.py --validate-config
```

## 🚨 故障排除

### 常见问题

1. **MongoDB 连接失败**
   - 检查 MongoDB 服务状态
   - 验证连接字符串配置
   - 确认网络连通性

2. **ORC 文件读取错误**
   - 检查文件路径和权限
   - 验证文件格式完整性
   - 确认磁盘空间充足

3. **内存不足**
   - 调整 batch_size 参数
   - 增加系统内存
   - 优化数据处理逻辑

4. **服务启动失败**
   - 检查端口占用情况
   - 验证配置文件格式
   - 查看详细错误日志

### 性能优化建议

- 根据系统资源调整批处理大小
- 使用 SSD 存储提升 I/O 性能
- 配置 MongoDB 索引优化查询
- 监控内存使用避免 OOM
- 合理设置延迟参数控制负载

## 📞 支持

如有问题或建议，请：

- 查看项目主 README 文档
- 提交 GitHub Issue
- 联系开发团队

---

**User-DF Team** - 专注于大规模用户数据处理解决方案
