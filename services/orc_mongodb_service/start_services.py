#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务启动脚本

启动ORC MongoDB服务的所有微服务：
- ORC处理微服务 (端口 8001)
- MongoDB写入微服务 (端口 8002)
"""

import os
import sys
import time
import argparse
import subprocess
import signal
import yaml
from typing import Dict, List, Optional
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger


class ORCMongoDBServiceManager:
    """ORC MongoDB服务管理器"""

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "configs/orc_mongodb_service/development.yaml"

        # 首先初始化ConfigManager，确保它使用正确的配置文件
        self.config_manager = ConfigManager(config_file=self.config_path)

        # 然后初始化Logger，此时ConfigManager已经加载了正确的配置
        self.logger = Logger.get_logger("ORCMongoDBServiceManager")

        # 从主配置文件加载服务配置
        self.service_configs = self._load_service_configs()

        # 服务配置
        self.services = {
            "orc_processor": {
                "name": "ORC处理微服务",
                "module": "services.orc_mongodb_service.orc_processor_service.main",
                "port": self._get_service_port("orc_processor_service", 8001),
                "process": None,
                "config_file": self.service_configs.get("orc_processor_service", {}).get("config_file")
            },
            "mongodb_writer": {
                "name": "MongoDB写入微服务",
                "module": "services.orc_mongodb_service.mongodb_writer_service.main",
                "port": self._get_service_port("mongodb_writer_service", 8002),
                "process": None,
                "config_file": self.service_configs.get("mongodb_writer_service", {}).get("config_file")
            }
        }

        # 进程管理
        self.processes = {}
        self.is_running = False

    def _get_service_port(self, service_key: str, default_port: int) -> int:
        """从子配置文件中读取服务端口号"""
        try:
            config_file = self.service_configs.get(service_key, {}).get("config_file")
            if not config_file or not os.path.exists(config_file):
                self.logger.warning(f"服务 {service_key} 配置文件不存在，使用默认端口 {default_port}")
                return default_port

            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}

            port = config.get("service", {}).get("port", default_port)
            self.logger.info(f"从配置文件读取 {service_key} 端口: {port}")
            return port

        except Exception as e:
            self.logger.warning(f"读取 {service_key} 端口配置失败: {e}，使用默认端口 {default_port}")
            return default_port

    def _load_service_configs(self) -> Dict[str, Dict[str, str]]:
        """从主配置文件加载服务配置文件路径"""
        try:
            if not os.path.exists(self.config_path):
                self.logger.warning(f"主配置文件不存在: {self.config_path}")
                return {}

            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}

            services_config = config.get("services", {})

            self.logger.info(f"从主配置文件加载了 {len(services_config)} 个服务配置")

            # 验证子服务配置文件是否存在
            self._validate_service_configs(services_config)

            return services_config

        except Exception as e:
            self.logger.error(f"加载主配置文件失败: {e}")
            return {}

    def _validate_service_configs(self, services_config: Dict):
        """验证子服务配置文件是否存在并可读取"""
        for service_name, service_info in services_config.items():
            config_file = service_info.get("config_file")
            if config_file:
                if os.path.exists(config_file):
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            yaml.safe_load(f)
                        self.logger.info(f"✅ {service_name} 配置文件验证通过: {config_file}")
                    except Exception as e:
                        self.logger.error(f"❌ {service_name} 配置文件格式错误: {config_file}, 错误: {e}")
                else:
                    self.logger.error(f"❌ {service_name} 配置文件不存在: {config_file}")
            else:
                self.logger.warning(f"⚠️  {service_name} 未配置 config_file，将使用主配置文件")

    def start_all_services(self):
        """启动所有服务"""
        try:
            self.logger.info("=== 启动ORC MongoDB服务微服务架构 ===")

            # 检查端口占用
            self._check_ports()

            # 启动各个服务
            for service_key, service_config in self.services.items():
                self._start_service_direct(service_key, service_config)

                # 等待服务启动
                time.sleep(2)

            # 等待所有服务就绪
            self._wait_for_services_ready()

            self.is_running = True
            self.logger.info("=== 所有ORC MongoDB服务微服务启动完成 ===")

            # 显示服务状态
            self._show_service_status()

        except Exception as e:
            self.logger.error(f"启动服务失败: {e}")
            self.stop_all_services()
            raise

    def _check_ports(self):
        """检查端口占用"""
        import socket

        for service_key, service_config in self.services.items():
            port = service_config["port"]

            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                result = sock.connect_ex(('localhost', port))
                if result == 0:
                    self.logger.warning(f"端口 {port} 已被占用 ({service_config['name']})")
                    # 尝试停止现有服务
                    self._kill_process_on_port(port)

    def _kill_process_on_port(self, port: int):
        """杀死占用指定端口的进程"""
        try:
            # 使用lsof查找占用端口的进程
            result = subprocess.run(
                ["lsof", "-ti", f":{port}"],
                capture_output=True,
                text=True
            )

            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid:
                        subprocess.run(["kill", "-9", pid])
                        self.logger.info(f"已杀死占用端口 {port} 的进程 {pid}")

        except Exception as e:
            self.logger.warning(f"无法杀死占用端口 {port} 的进程: {e}")



    def _start_service_direct(self, service_key: str, service_config: Dict):
        """直接启动服务"""
        try:
            module_name = service_config["module"]
            port = service_config["port"]
            service_config_file = service_config.get("config_file")

            # 构建启动命令
            cmd = [
                sys.executable, "-m", module_name,
                "--host", "0.0.0.0",
                "--port", str(port)
            ]

            # 使用服务特定的配置文件，如果没有则使用主配置文件
            config_file_to_use = service_config_file or self.config_path
            if config_file_to_use:
                cmd.extend(["--config", config_file_to_use])

            # 验证配置文件是否存在
            if config_file_to_use and not os.path.exists(config_file_to_use):
                self.logger.error(f"配置文件不存在: {config_file_to_use}")
                raise FileNotFoundError(f"配置文件不存在: {config_file_to_use}")

            # 设置环境变量
            env = os.environ.copy()

            # 设置PYTHONPATH确保模块能正确导入
            current_pythonpath = env.get('PYTHONPATH', '')
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            if project_root not in current_pythonpath:
                env['PYTHONPATH'] = f"{project_root}:{current_pythonpath}" if current_pythonpath else project_root

            # 启动进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )

            self.processes[service_key] = process
            self.logger.info(f"已启动 {service_config['name']} (PID: {process.pid}, 配置: {config_file_to_use})")

        except Exception as e:
            self.logger.error(f"直接启动服务失败 {service_key}: {e}")
            raise

    def _wait_for_services_ready(self, timeout: int = 60):
        """等待所有服务就绪"""
        self.logger.info("等待所有服务就绪...")

        start_time = time.time()

        while time.time() - start_time < timeout:
            all_ready = True

            for service_key, service_config in self.services.items():
                port = service_config["port"]

                try:
                    response = requests.get(f"http://localhost:{port}/health", timeout=2)
                    if response.status_code != 200:
                        all_ready = False
                        break
                except:
                    all_ready = False
                    break

            if all_ready:
                self.logger.info("所有服务已就绪")
                return

            time.sleep(2)

        raise Exception(f"等待服务就绪超时 ({timeout}秒)")

    def _show_service_status(self):
        """显示服务状态"""
        self.logger.info("\n=== 服务状态 ===")

        for service_key, service_config in self.services.items():
            name = service_config["name"]
            port = service_config["port"]

            try:
                response = requests.get(f"http://localhost:{port}/health", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    status = "✅ 运行中"
                    uptime = data.get("uptime", 0)
                    version = data.get("version", "unknown")
                    self.logger.info(f"{name}: {status} (端口: {port}, 运行时间: {uptime:.1f}s, 版本: {version})")
                else:
                    self.logger.info(f"{name}: ❌ 异常 (端口: {port}, HTTP {response.status_code})")
            except Exception as e:
                self.logger.info(f"{name}: ❌ 无法连接 (端口: {port}, 错误: {e})")

        self.logger.info("\n=== 管理命令 ===")
        self.logger.info("查看服务日志: tmux attach -t <session-name>")
        self.logger.info("停止所有服务: python3 services/orc_mongodb_service/start_services.py --stop")
        self.logger.info("监控服务状态: python3 -m services.orc_mongodb_service.monitoring_service.monitor")

    def stop_all_services(self):
        """停止所有服务"""
        try:
            self.logger.info("=== 停止ORC MongoDB服务微服务 ===")

            # 停止tmux会话
            for service_key, service_config in self.services.items():
                session_name = service_config["tmux_session"]

                try:
                    # 检查会话是否存在
                    check_session = subprocess.run(
                        ["tmux", "has-session", "-t", session_name],
                        capture_output=True
                    )

                    if check_session.returncode == 0:
                        subprocess.run(["tmux", "kill-session", "-t", session_name])
                        self.logger.info(f"已停止tmux会话: {session_name}")

                except Exception as e:
                    self.logger.warning(f"停止tmux会话失败 {session_name}: {e}")

            # 停止直接启动的进程
            for service_key, process in self.processes.items():
                try:
                    if process and process.poll() is None:
                        process.terminate()
                        process.wait(timeout=5)
                        self.logger.info(f"已停止进程: {service_key}")
                except Exception as e:
                    self.logger.warning(f"停止进程失败 {service_key}: {e}")

            self.is_running = False
            self.logger.info("=== 所有ORC MongoDB服务微服务已停止 ===")

        except Exception as e:
            self.logger.error(f"停止服务失败: {e}")


    def check_status(self):
        """检查服务状态"""
        self.logger.info("=== ORC MongoDB服务状态检查 ===")

        for service_key, service_config in self.services.items():
            name = service_config["name"]
            port = service_config["port"]

            # 检查进程状态
            process = self.processes.get(service_key)
            if process:
                if process.poll() is None:
                    process_status = f"✅ 运行中 (PID: {process.pid})"
                else:
                    process_status = f"❌ 已停止 (退出码: {process.returncode})"
            else:
                process_status = "❌ 未启动"

            # 检查HTTP服务
            try:
                response = requests.get(f"http://localhost:{port}/health", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    http_status = f"✅ 健康 (运行时间: {data.get('uptime', 0):.1f}s)"
                else:
                    http_status = f"❌ 异常 (HTTP {response.status_code})"
            except Exception as e:
                http_status = f"❌ 无法连接 ({str(e)[:50]})"

            self.logger.info(f"{name}:")
            self.logger.info(f"  端口: {port}")
            self.logger.info(f"  进程状态: {process_status}")
            self.logger.info(f"  HTTP状态: {http_status}")
            self.logger.info("")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="ORC MongoDB服务管理器")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--stop", action="store_true", help="停止所有服务")
    parser.add_argument("--status", action="store_true", help="检查服务状态")
    parser.add_argument("--check-status", action="store_true", help="检查服务状态（别名）")
    parser.add_argument("--validate-config", action="store_true", help="验证配置文件")


    args = parser.parse_args()

    try:
        # 设置环境变量
        if args.config:
            os.environ['USER_DF_CONFIG_FILE'] = args.config

        manager = ORCMongoDBServiceManager(args.config)

        if args.validate_config:
            # 验证配置文件
            print("配置文件验证通过 ✅")
            return
        elif args.stop:
            manager.stop_all_services()
        elif args.status or args.check_status:
            manager.check_status()
        else:
            # 设置信号处理
            def signal_handler(signum, frame):
                print("\n接收到中断信号，正在停止服务...")
                manager.stop_all_services()
                sys.exit(0)

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            # 启动服务
            manager.start_all_services()

            # 保持运行
            try:
                while manager.is_running:
                    time.sleep(1)
            except KeyboardInterrupt:
                manager.stop_all_services()

    except Exception as e:
        print(f"服务管理失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
