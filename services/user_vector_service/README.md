# 用户向量服务

用户向量服务是 User-DF 系统的向量化处理服务，负责从 MongoDB 读取用户数据，通过 Milvus 获取内容向量，执行 PCA 降维计算用户向量，并将结果存储回 Milvus。该服务采用微服务架构，支持大规模向量计算和高并发处理。

## 🏗️ 服务架构

该服务包含三个微服务：

### MongoDB 读取微服务 (端口 8003)

- **功能**: 从 MongoDB 读取需要处理的用户数据
- **特性**:
  - 批量读取 (100,000 用户/批次)
  - 智能过滤 (vector_status.is_stored=false)
  - 乐观锁机制防止重复处理
  - 支持按 _id 范围查询

### 向量计算微服务 (端口 8004)

- **功能**: 执行向量计算和 PCA 降维
- **特性**:
  - 从 Milvus 批量获取内容向量 (512D)
  - PCA 降维处理 (512D → 256D)
  - 预计算 PCA 模型支持
  - 高效的向量运算优化

### 向量存储微服务 (端口 8005)

- **功能**: 将用户向量存储到 Milvus
- **特性**:
  - 批量向量存储
  - 自动索引管理
  - 存储状态更新
  - 错误恢复机制

## 🚀 快速开始

### 启动服务

```bash
# 启动所有微服务
python3 services/user_vector_service/start_services.py

# 使用指定配置启动
python3 services/user_vector_service/start_services.py --config configs/user_vector_service/production.yaml

# 检查服务状态
python3 services/user_vector_service/start_services.py --status

# 停止所有服务
python3 services/user_vector_service/start_services.py --stop
```

### 服务管理

```bash
# 查看服务进程
ps aux | grep user_vector_service

# 监控服务状态
python3 -m services.user_vector_service.monitoring_service.monitor

# 查看实时日志
tail -f logs/user_vector_service_*.log
```

## ⚙️ 配置说明

### 主要配置文件

- `configs/user_vector_service/development.yaml` - 开发环境配置
- `configs/user_vector_service/production.yaml` - 生产环境配置

### 核心配置项

```yaml
# MongoDB 配置
mongodb:
  connection_string: "mongodb://localhost:27017"
  database: "nrdc"
  collection: "user_pid_records_optimized"
  batch_size: 100000                  # 批量读取大小

# Milvus 配置
milvus:
  uri: "http://localhost:19530"
  token: ""
  database: "nrdc_db"
  content_collection: "content_tower_collection_20250616"
  user_collection: "user_tower_collection"

# PCA 配置
pca:
  model_path: "models/pca_precomputed/pca_model.pkl"
  input_dim: 512                      # 输入维度
  output_dim: 256                     # 输出维度
  
# 性能优化配置
performance:
  vector_batch_size: 15000            # 向量批处理大小
  max_memory_usage: "16GB"            # 最大内存使用
  concurrent_workers: 4               # 并发工作线程
```

## 📊 数据处理流程

1. **用户数据读取**: 从 MongoDB 批量读取需要处理的用户数据
2. **PID 收集**: 收集用户的所有 PID 列表
3. **向量查询**: 从 Milvus 批量查询 PID 对应的内容向量
4. **PCA 降维**: 使用预训练 PCA 模型进行降维 (512D → 256D)
5. **用户向量计算**: 计算用户的平均向量表示
6. **向量存储**: 将用户向量存储到 Milvus 用户向量集合
7. **状态更新**: 更新 MongoDB 中的向量存储状态

## 🗄️ 数据模型

### Milvus 集合结构

#### 内容向量集合 (content_tower_collection_20250616)

```json
{
  "item_id": 123456789,               # 内容ID (INT64)
  "item_source": "source_name",       # 内容来源 (VARCHAR)
  "item_embedding": [0.1, 0.2, ...], # 内容向量 (FLOAT_VECTOR 512D)
  "category_level4_id": 1001,         # 四级分类ID (INT64)
  "timestamp": 1627776000             # 时间戳 (INT64)
}
```

#### 用户向量集合 (user_tower_collection)

```json
{
  "user_id": 123456789,               # 用户ID (INT64)
  "user_embedding": [0.1, 0.2, ...]  # 用户向量 (FLOAT_VECTOR 256D)
}
```

## 📈 性能特性

- **大规模处理**: 支持 20 亿用户的向量化处理
- **批量优化**: 批量向量查询和存储提升性能
- **内存管理**: 智能的内存使用和垃圾回收
- **并发处理**: 多微服务并行处理
- **PCA 优化**: 预计算 PCA 模型提升降维速度

## 🔍 监控和日志

### 日志文件

- `logs/user_vector_service_YYYYMMDD_HHMMSS.log` - 主服务日志
- 每个微服务独立运行，可单独查看日志

### 监控指标

- 处理速度 (用户/秒)
- 向量查询性能
- PCA 降维耗时
- 内存使用情况
- Milvus 连接状态
- 错误率和重试次数

## 🛠️ 开发和调试

### 本地开发

```bash
# 安装开发依赖
pip install -e .[dev]

# 运行单元测试
pytest tests/services/user_vector_service/

# 代码格式化
black services/user_vector_service/
isort services/user_vector_service/
```

### 调试模式

```bash
# 查看详细日志
tail -f logs/user_vector_service_*.log

# 验证配置文件
python3 services/user_vector_service/start_services.py --validate-config

# 测试 PCA 模型
python3 -c "
import pickle
with open('models/pca_precomputed/pca_model.pkl', 'rb') as f:
    pca = pickle.load(f)
    print(f'PCA 模型: {pca.n_components_} 维输出')
"
```

## 🚨 故障排除

### 常见问题

1. **Milvus 连接失败**
   - 检查 Milvus 服务状态
   - 验证连接配置和认证信息
   - 确认网络连通性

2. **PCA 模型加载失败**
   - 检查模型文件路径和权限
   - 验证模型文件完整性
   - 确认模型版本兼容性

3. **内存不足**
   - 调整 batch_size 参数
   - 增加系统内存
   - 优化向量处理逻辑

4. **向量查询超时**
   - 检查 Milvus 服务性能
   - 调整查询超时设置
   - 优化查询批次大小

### 性能优化建议

- 根据系统资源调整批处理大小
- 使用 GPU 加速 PCA 计算
- 配置 Milvus 索引优化查询
- 监控内存使用避免 OOM
- 合理设置并发工作线程数

## 📞 支持

如有问题或建议，请：

- 查看项目主 README 文档
- 提交 GitHub Issue
- 联系开发团队

---

**User-DF Team** - 专注于大规模用户数据处理解决方案
